﻿using BIM4U.Data;
using DevExpress.Spreadsheet;
using DevExpress.XtraSpreadsheet;
using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace BIM4U.Import
{
    public class ExcelDataImporter
    {
        #region EventsAndDelegates
        public event OnErrorDelegate? OnError;
        #endregion

        /// <summary>
        /// Processes worksheet and returns a list of ExcelData records.
        /// </summary>
        /// <param name="filePath"></param>
        /// <returns></returns>
        /// <exception cref="ArgumentException"></exception>
        /// <exception cref="FileNotFoundException"></exception>
        public Task<List<ExcelData>> GetListOfExcelDataRecordsFromFile(Worksheet sheet, Action<string>? progressDel = null)
        {
            if (sheet == null)
            {
                throw new ArgumentNullException(nameof(sheet));
            }

            var excelDataList = new List<ExcelData>();
            var usedRange = sheet.GetDataRange();

            int companyNameColIndex = 0;
            int companyLogoUrlColIndex = 1;
            int productNameColIndex = 2;
            int productDescriptionColIndex = 3;
            int productSpecificationColIndex = 4;
            int productCodeColIndex = 5; //This is new column added to the excel sheet to store product code.
            int productImageUrlColIndex = 6;
            int productTechnicalImageUrlColIndex = 8;

            // Colour-related columns (replacing additional product images)
            int colour1ImageColIndex = 9; // Excel column J (0-based index 9)
            int colour1CategoryTitleColIndex = 10; // Excel column K (0-based index 10)
            int colour2ImageColIndex = 11; // Excel column L (0-based index 11)
            int colour2CategoryTitleColIndex = 12; // Excel column M (0-based index 12)
            int colour3ImageColIndex = 13; // Excel column N (0-based index 13)
            int colour3CategoryTitleColIndex = 14; // Excel column O (0-based index 14)
            int catalogueUrlColIndex = 15;

            // Contact information columns (R, S, T)
            int websiteUrlColIndex = 17; // Excel column R (0-based index 17)
            int emailAddressColIndex = 18; // Excel column S (0-based index 18)
            int phoneNumbersColIndex = 19; // Excel column T (0-based index 19)

            int rowIndex = 1; // Row 0 should contain column headers.
            int failedRowsCount = 0;

            // Extract column headers from row 0 for dynamic image section title
            string productImageColumnHeader = "Product Image"; // Default fallback
            string technicalDrawingColumnHeader = "Technical Drawing"; // Default fallback

            try
            {
                if (usedRange.RowCount > 0)
                {
                    productImageColumnHeader = sheet[0, productImageUrlColIndex].Value.TextValue ?? "Product Image";
                    technicalDrawingColumnHeader = sheet[0, productTechnicalImageUrlColIndex].Value.TextValue ?? "Technical Drawing";
                }
            }
            catch (Exception)
            {
                // Use default values if header extraction fails
                productImageColumnHeader = "Product Image";
                technicalDrawingColumnHeader = "Technical Drawing";
            }

            for (int i = rowIndex; i <= usedRange.BottomRowIndex; i++)
            {
                rowIndex = i;
                if (progressDel != null)
                {
                    progressDel.Invoke(string.Format("Processing row {0} of {1} | Failed rows: {2}", i + 1, usedRange.RowCount, failedRowsCount));
                }
                try
                {
                    ExcelData data = new ExcelData();
                    data.RowIndex = i;
                    data.CompanyName = sheet[rowIndex, companyNameColIndex].Value.TextValue;
                    data.CompanyLogoUrl = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, companyLogoUrlColIndex]);
                    data.ProductName = sheet[rowIndex, productNameColIndex].Value.TextValue;
                   // data.ProductCode = sheet[rowIndex, productCodeColIndex].Value.TextValue;
                    data.ProductDescription = sheet[rowIndex, productDescriptionColIndex].Value.TextValue;
                    data.ProductSpecification = sheet[rowIndex, productSpecificationColIndex].Value.TextValue;
                    data.ProductImageUrl = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, productImageUrlColIndex]);
                    data.ProductTechnicalImageUrl = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, productTechnicalImageUrlColIndex]);

                    //I've commented out Product Code extraction line above, and replaced it with the code snippet below,
                    //This is will make sure that the value found, if it's a numeric value, it's converted to a string, because otherwise it will return null. 
                    var cellValue = sheet[rowIndex, productCodeColIndex].Value;
                    if (cellValue.IsNumeric)
                        data.ProductCode = cellValue.NumericValue.ToString();
                    else
                        data.ProductCode = cellValue.TextValue;

                    //Product name is used for most export operations. Validate and throw an error
                    if (string.IsNullOrWhiteSpace(data.ProductName))
                    {
                        throw new ArgumentException("Row is missing required parameter: ProductName. Row " + rowIndex);
                    }

                    //validate product image url and technical image url
                    if (!string.IsNullOrEmpty(data.ProductImageUrl) && !this.IsWellFormedUriString(data.ProductImageUrl, UriKind.Absolute))
                    {
                        string msg = string.Format("Invalid product image url at row {0} and column {1}", rowIndex, productImageUrlColIndex);
                        throw new UriFormatException(msg);
                    }

                    if (!string.IsNullOrEmpty(data.ProductTechnicalImageUrl) && !this.IsWellFormedUriString(data.ProductTechnicalImageUrl, UriKind.Absolute))
                    {
                        string msg = string.Format("Invalid product technical image url at row {0} and column {1}", rowIndex, productTechnicalImageUrlColIndex);
                        throw new UriFormatException(msg);
                    }

                    // Extract colour-related images and titles for Available Colours section
                    data.Colour1Image = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, colour1ImageColIndex]);
                    data.Colour1CategoryTitle = sheet[rowIndex, colour1CategoryTitleColIndex].Value.TextValue;
                    data.Colour2Image = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, colour2ImageColIndex]);
                    data.Colour2CategoryTitle = sheet[rowIndex, colour2CategoryTitleColIndex].Value.TextValue;
                    data.Colour3Image = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, colour3ImageColIndex]);
                    data.Colour3CategoryTitle = sheet[rowIndex, colour3CategoryTitleColIndex].Value.TextValue;

                    // Validate colour image URLs if provided
                    if (!string.IsNullOrEmpty(data.Colour1Image) && !this.IsWellFormedUriString(data.Colour1Image, UriKind.Absolute))
                    {
                        string msg = string.Format("Invalid colour 1 image url at row {0} and column {1}", rowIndex, colour1ImageColIndex);
                        throw new UriFormatException(msg);
                    }

                    if (!string.IsNullOrEmpty(data.Colour2Image) && !this.IsWellFormedUriString(data.Colour2Image, UriKind.Absolute))
                    {
                        string msg = string.Format("Invalid colour 2 image url at row {0} and column {1}", rowIndex, colour2ImageColIndex);
                        throw new UriFormatException(msg);
                    }

                    if (!string.IsNullOrEmpty(data.Colour3Image) && !this.IsWellFormedUriString(data.Colour3Image, UriKind.Absolute))
                    {
                        string msg = string.Format("Invalid colour 3 image url at row {0} and column {1}", rowIndex, colour3ImageColIndex);
                        throw new UriFormatException(msg);
                    }

                    data.CatalogueUrl = GetHyperLinkUrlOrTextValue(sheet, sheet[rowIndex, catalogueUrlColIndex]);
                    //validate catalogue url
                    if (!string.IsNullOrEmpty(data.CatalogueUrl) && !this.IsWellFormedUriString(data.CatalogueUrl, UriKind.Absolute))
                    {
                        string msg = string.Format("Invalid catalogue url at row {0} and column {1}", rowIndex, catalogueUrlColIndex);
                        throw new UriFormatException(msg);
                    }

                    // Extract contact information from row 2 (index 1) - these are global contact details
                    if (rowIndex == 1) // Only extract contact info from row 2 (index 1)
                    {
                        data.WebsiteUrl = sheet[1, websiteUrlColIndex].Value.TextValue;
                        data.EmailAddress = sheet[1, emailAddressColIndex].Value.TextValue;
                        data.PhoneNumbers = sheet[1, phoneNumbersColIndex].Value.TextValue;

                        // Validate website URL
                        if (!string.IsNullOrEmpty(data.WebsiteUrl) && !this.IsWellFormedUriString(data.WebsiteUrl, UriKind.Absolute))
                        {
                            string msg = string.Format("Invalid website url at row 2 and column {0}", websiteUrlColIndex);
                            throw new UriFormatException(msg);
                        }

                        // Validate email address format
                        if (!string.IsNullOrEmpty(data.EmailAddress) && !IsValidEmail(data.EmailAddress))
                        {
                            string msg = string.Format("Invalid email address format at row 2 and column {0}", emailAddressColIndex);
                            throw new ArgumentException(msg);
                        }
                    }
                    else
                    {
                        // For other rows, copy contact info from the first data row (row 2)
                        if (excelDataList.Count > 0)
                        {
                            data.WebsiteUrl = excelDataList[0].WebsiteUrl;
                            data.EmailAddress = excelDataList[0].EmailAddress;
                            data.PhoneNumbers = excelDataList[0].PhoneNumbers;
                        }
                    }

                    // Add column headers for dynamic image section title
                    data.ProductImageColumnHeader = productImageColumnHeader;
                    data.TechnicalDrawingColumnHeader = technicalDrawingColumnHeader;

                    excelDataList.Add(data);
                }
                catch (Exception ex)
                {
                    OnError?.Invoke(rowIndex, ex.Message);
                    if (progressDel != null)
                    {
                        progressDel.Invoke(string.Format("Processing row {0} of {1} | Failed rows: {2}", i + 1, usedRange.RowCount, failedRowsCount));
                    }
                }
            }

            return Task.FromResult(excelDataList);
        }

        /// <summary>
        /// Returns the hyperlink url or the cell value if a hyperlink is not present.
        /// </summary>
        /// <param name="cell"></param>
        /// <returns></returns>
        private string? GetHyperLinkUrlOrTextValue(Worksheet sheet, Cell cell)
        {
            return sheet.Hyperlinks.GetHyperlinks(cell).FirstOrDefault()?.Uri ?? cell.Value.TextValue;
        }

        private bool IsWellFormedUriString(string uri, UriKind uriKind)
        {
            if (string.IsNullOrEmpty(uri))
            {
                throw new ArgumentNullException(nameof(uri));
            }

            var checkUri = new Uri(uri);

            return Uri.IsWellFormedUriString(uri, uriKind) || checkUri.IsAbsoluteUri;
        }

        private bool IsValidEmail(string email)
        {
            if (string.IsNullOrWhiteSpace(email))
                return false;

            try
            {
                // Use System.Net.Mail.MailAddress to validate email format
                var mailAddress = new System.Net.Mail.MailAddress(email);
                return mailAddress.Address == email;
            }
            catch
            {
                return false;
            }
        }
    }
}
