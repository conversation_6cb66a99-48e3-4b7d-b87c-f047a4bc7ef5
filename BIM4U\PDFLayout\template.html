﻿<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>Product Datasheet</title>
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">
  <style>
/* FINAL CUSTOM CSS - Only absolute essentials that <PERSON><PERSON><PERSON> cannot handle */

/* Logo sizing with left drop shadow */
#headerLogo {
  width: 50%;
  height: auto;
  max-width: 100%;
  display: block;
  margin-left: auto;
  margin-right: auto;
  box-shadow: -5px -5px 10px rgba(0, 0, 0, 0.5);
}

#footerLogo {
  width: 70%;
  height: 70%;
  object-fit: contain;
}

 /* .cellWidth{
  width: 600PX;
} */
 .descSectionOverlay{
  z-index: 2;
  top: 60px;
  position: relative;
 }
 .columnLayout{
  /* z-index: 2; */
  top: 15px;
  right: 7px;
  position: relative;
 }


#mainContent {
  background: #a6a9ab !important;
  max-width: 800px;
  min-height: 1000px;
  /* border: 3px solid #000000; */
  font-size: 0.92em;
  -webkit-print-color-adjust: exact;
  print-color-adjust: exact;
}

#imageBox {
  background: #a6a9ab;
  border: 1px solid #a6a9ab;
  overflow: hidden;
  width: 100%;
  max-width: 700px;
  height: 240px;
  margin: 0 auto;
  padding: 0;
}

#imageBox .row {
  margin: 0;
  height: 100%;
}

#imageBox .col-6 {
  padding: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

#imageBox img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  margin: 0;
  padding: 0;
}

/* Single image layout styles */
#imageBox .col-12 {
  padding: 0;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

#imageBox .col-12 img {
  width: 100%;
  height: 100%;
  object-fit: contain;
  display: block;
  margin: 0;
  padding: 0;
  max-height: 240px; /* Same height as the imageBox container */
}

/* Image Section Title Bar - Black background with white text */
.image-section-title-bar {
  background-color: #000000;
  color: #ffffff;
  font-size: 0.92em;
  letter-spacing: 0.5px;
  font-style: italic;
  margin-left: auto;
  margin-right: auto;
  max-width: 700px;
  width: 100%;
}

/* Available Colours Section */
.available-colours-section {
  width: 100%;
  max-width: 700px;
  margin: 10px auto 0 auto;
}

.colours-title-bar {
  background-color: #ffffff;
  color: #000000;
  font-size: 0.92em;
  letter-spacing: 0.5px;
  font-style: italic;
  width: 100%;
}

.colours-container {
  background-color: #000000;
  padding: 15px;
  max-height: 280px;
  /* overflow: hidden; */
  /* align-content: start; */
}

.colours-header-row {
  display: flex;
  margin-bottom: 10px;
}

.colour-category {
  flex: 1;
  text-align: left;
}

.category-title {
  color: #b5880b;
  /* font-weight: bold; */
  font-size: 1.2em;
  letter-spacing: 0.7px;
  text-transform: uppercase;
  font-style: italic;
  /* margin-bottom: 10px; */
}

.colours-content-row {
  display: flex;
}

.colour-images-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  gap: 10px;
}

.colour-images-group:first-child {
  flex-direction: row;
  justify-content: flex-start;
  gap: 15px;
}

/* Third color column styling */
.colour-category-3,
.colour-images-group-3 {
  display: block; /* Always show, will be hidden via content if empty */
}

/* Responsive layout for 3 columns */
.colours-header-row .colour-category {
  flex: 1;
  min-width: 0; /* Allow flex items to shrink */
}

.colours-content-row .colour-images-group {
  flex: 1;
  min-width: 0; /* Allow flex items to shrink */
}

.colour-item {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.colour-image {
  max-width: 100px;
  max-height: 80px;
  object-fit: contain;
  margin-bottom: 5px;
}



#productNameLabel,
#productCodeLabel,
#descLabel,
#detailedDescLabel,
#companyLabel {
  font-size: 0.92em;
  letter-spacing: 0.5px;
  font-style: italic;
}

#footerTable td.fw-bold {
  letter-spacing: 0.5px;
}
#footerTable td{
  background-color: #a6a9ab;
}

/* PRINT STYLES - Critical for PDF generation */
@media print {
  @page {
    size: A4;
    margin: 1.2cm;
    background: #a6a9ab;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Ensure all pages have consistent background */
  @page :first {
    background: #a6a9ab;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  @page :left {
    background: #a6a9ab;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  @page :right {
    background: #a6a9ab;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* html {
    background: #bfbfbf !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  } */

  body {
    background: #a6a9ab !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    margin: 0;
    font-size: 0.92em;
  }

  #mainContent {
    width: 18cm !important;
    max-width: none !important;
    min-height: calc(29.7cm - 2.4cm) !important;
    padding: 1.5rem 2rem !important;
    margin: 0 !important;
    font-size: 0.92em !important;
    background: #a6a9ab !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    box-sizing: border-box !important;
    display: flex !important;
    flex-direction: column !important;
    border-radius: 0 !important;
  }

  #bottomGroup {
    margin-top: auto !important;
  }

  /* Prevent specific sections from breaking across pages */
  #imageBox,
  #bottomGroup,
  footer {
    break-inside: avoid;
    page-break-inside: avoid;
  }

  /* Ensure all elements maintain background consistency */
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Additional page break handling for long content */
  .descSectionOverlay {
    page-break-inside: auto;
    background: inherit;
  }

  /* Ensure proper page breaks for overflow content */
  @page :blank {
    background: #a6a9ab;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }



  /* Force content to maintain structure across pages */
  body {
    overflow: visible;
  }

  /* Ensure continuation pages have proper structure */
  .page-break {
    page-break-before: always;
    min-height: calc(100vh - 4cm); /* Account for margins */
    /* border: 3px solid #000000; */
    background: #a6a9ab !important;
    padding: 1.5rem 2rem;
    box-sizing: border-box;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Ensure all elements maintain background consistency */
  * {
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  /* Fix footer table width overflow */
  #footerTable {
    width: 100% !important;
    max-width: 100% !important;
    table-layout: fixed !important;
    overflow: hidden !important;
  }

  #footerTable td {
    overflow: hidden !important;
    word-wrap: break-word !important;
    word-break: break-word !important;
  }


  /* Print styles for title bars and sections */
  .image-section-title-bar {
    background-color: #000000 !important;
    color: #ffffff !important;
    font-size: 0.92em;
    letter-spacing: 0.5px;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .colours-title-bar {
    background-color: #ffffff !important;
    color: #000000 !important;
    font-size: 0.92em;
    letter-spacing: 0.5px;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .available-colours-section {
    page-break-inside: avoid;
    width: 100% !important;
    max-width: 700px !important;
    margin: 20px auto 0 auto !important;
  }

  .colours-container {
    background-color: #000000 !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
    padding: 15px !important;
    max-height: 320px !important;
    overflow: hidden !important;
  }

  .category-title {
    color: #b5880b!important;
    font-style: italic !important;
    -webkit-print-color-adjust: exact;
    print-color-adjust: exact;
  }

  .colour-image {
    max-width: 140px !important;
    max-height: 120px !important;
    object-fit: contain !important;
  }

  /* Print styles for third color column */
  .colours-header-row {
    display: flex !important;
    gap: 10px !important;
  }

  .colours-content-row {
    display: flex !important;
    gap: 10px !important;
  }

  .colour-category,
  .colour-images-group {
    flex: 1 !important;
    min-width: 0 !important;
  }

  /* Adjust image sizes for 3-column layout */
  .colours-content-row .colour-image {
    max-width: 120px !important;
    max-height: 100px !important;
  }

  #productNameLabel,
  #productCodeLabel,
  #descLabel,
  #detailedDescLabel,
  #companyLabel {
    font-size: 0.92em;
    letter-spacing: 0.5px;
  }

  #imageBox {
    background: #a6a9ab !important;
    border: 1px solid #a6a9ab!important;
    width: 100% !important;
    max-width: 700px !important;
    height: 240px !important;
    margin: 0 auto !important;
    padding: 0 !important;
    page-break-inside: avoid;
  }

  #imageBox .row {
    display: flex !important;
    flex-direction: row !important;
    flex-wrap: nowrap !important;
    margin: 0 !important;
    height: 100% !important;
  }

  #imageBox .col-6 {
    width: 50% !important;
    max-width: 50% !important;
    flex: 0 0 50% !important;
    padding: 0 !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  #imageBox img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
  }

  /* Print styles for single image layout */
  #imageBox .col-12 {
    width: 100% !important;
    max-width: 100% !important;
    flex: 0 0 100% !important;
    padding: 0 !important;
    height: 100% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
  }

  #imageBox .col-12 img {
    width: 100% !important;
    height: 100% !important;
    object-fit: contain !important;
    display: block !important;
    margin: 0 !important;
    padding: 0 !important;
    max-height: 240px !important; /* Same height as the imageBox container */
  }

  #headerLogo {
    box-shadow: -5px 5px 10px rgba(0, 0, 0, 0.3) !important;
  }

  #footerLogo {
    width: 100%;
    height: 100%;
    object-fit: contain;
  }

  a {
    color: black;
    text-decoration: none;
  }

  img {
    max-width: 100%;
    height: auto;
  }

  #footerTable td.fw-bold {
    letter-spacing: 0.5px;
  }
}

#footerTable td:last-child img {
  height: 100px;
  max-width: 100%;
  object-fit: contain;
  display: block;
  margin-left: auto;
}
  </style>
</head>

<body class="bg-white">
  <div id="mainContent" class="container-fluid mx-auto my-4 p-4 d-flex flex-column"
       style="max-width: 800px; min-height: 1000px; font-size: 0.92em;">

    <!--top logo section -->
    <div class="row align-items-center mb-4">
      <div class="col text-center">
        {{CompanyLogo}}
      </div>
    </div>

    <!-- Product name and product code section -->
    <div class="mb-3">
      <div class="fw-bold text-uppercase mb-1 lh-1" id="productNameLabel" name="productNameLabel">Product Names:</div>
      <div class="mb-2 lh-sm" id="productName" name="productName">{{ProductName}}</div>
      <div class="fw-bold text-uppercase mb-1 lh-1 mt-4" id="productCodeLabel" name="productCodeLabel">Product Code:</div>
      <div class="mb-2 lh-sm" id="productCode" name="productCode">{{ProductCode}}</div>
    </div>

    <!-- Product image and technical drawing section -->
    <div class="image-section-title-bar fw-bold text-uppercase mb-0 lh-1 p-2" id="imageSectionTitle" name="imageSectionTitle" style="display: {{ImageBoxDisplay}};">{{ImageSectionTitle}}</div>
    <div id="imageBox" name="imageBox" class="mb-4" style="display: {{ImageBoxDisplay}};">
      <div class="row">
        <div class="{{ProductImageColumnClass}}">
          {{ProductImage}}
        </div>
        <div class="{{TechnicalImageColumnClass}}">
          {{TechnicalDrawing}}
        </div>
      </div>
    </div>

    <!-- Available Colours section -->
    <div class="available-colours-section mb-4">
      <div class="colours-title-bar fw-bold text-uppercase mb-0 lh-1 p-1" id="coloursTitle" name="coloursTitle">Available Colours</div>
      <div id="coloursBox" name="coloursBox" class="colours-container">
        <div class="colours-header-row">
          <div class="colour-category">
            <div class="category-title">{{Colour1CategoryTitle}}</div>
          </div>
          <div class="colour-category">
            <div class="category-title">{{Colour2CategoryTitle}}</div>
          </div>
          <div class="colour-category colour-category-3" style="display: {{Colour3CategoryDisplay}};">
            <div class="category-title">{{Colour3CategoryTitle}}</div>
          </div>
        </div>
        <div class="colours-content-row">
          <div class="colour-images-group">
            <div class="colour-item">
              {{Colour1Image}}
            </div>
          </div>
          <div class="colour-images-group">
            <div class="colour-item">
              {{Colour2Image}}
            </div>
          </div>
          <div class="colour-images-group colour-images-group-3" style="display: {{Colour3ImagesDisplay}};">
            <div class="colour-item">
              {{Colour3Image}}
            </div>
          </div>
        </div>
      </div>
    </div>

        <div id="bottomGroup" class="mt-auto">
            <div class="descSectionOverlay">
                <div class="mb-3">
                    <div class="fw-bold text-uppercase mb-1 lh-1" id="descLabel">Description:</div>
                    <div class="lh-sm" id="descValue">{{ProductDescription}}</div>
                </div>
                <div class="mb-3">
                    <div class="fw-bold text-uppercase mb-1 lh-1" id="detailedDescLabel">Detailed Description:</div>
                    <div class="lh-sm" id="detailedDescValue">{{ProductSpecification}}</div>
                </div>
                <div class="mb-1">
                    <div class="fw-bold text-uppercase mb-1 lh-1" id="companyLabel">Company:</div>
                    <div class="lh-sm" id="companyName">{{CompanyName}}</div>
                </div>
            </div>
            <div class="text-center">{{ExtraImages}}</div>
            <footer class="pt-1" style="font-size: 0.6em;" id="footer">
                <table class="table table-borderless mb-0 w-100 align-middle" id="footerTable" style="table-layout: fixed;">
                    <tbody>
                        <tr>
                            <td class="fw-bold text-uppercase text-start columnLayout" style="width:10%; vertical-align:middle;">
                                WEBSITE:<br>EMAIL:<br>CALL:
                            </td>
                            <td class="text-start columnLayout" style="width:73%; vertical-align:middle;">
                                <a href="https://www.kirk.co.za" target="_blank" class="text-dark text-decoration-none">www.kirk.co.za</a><br>
                                <a href="mailto:<EMAIL>" class="text-dark text-decoration-none"><EMAIL></a><br>
                                JHB: (+27) 011 444 1441 &nbsp;|&nbsp;&nbsp; CT: (+27) 021 949 2226 &nbsp;|&nbsp;&nbsp; DBN: (+27) 031 564 1175 &nbsp;|&nbsp;&nbsp; PE: (+27) 41 010 0086 / 086 044 7573
                            </td>
                            <td class="text-end align-top" style="width:17%; vertical-align:top;">
                                <img src="data:image/jpeg;base64,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"           
             alt="BIM4U Logo" id="footerLogo" style="height:100px; max-width:100%; object-fit:contain; display:block; margin-left:auto;">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </footer>
        </div>
    </div>
</body>
</html>
