﻿using BIM4U.Data;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using ceTe.DynamicPDF.HtmlConverter;
using static DevExpress.XtraPrinting.Native.ExportOptionsPropertiesNames;
using DevExpress.XtraRichEdit.Model;
using System.IO;



namespace BIM4U.Exporters
{
    public class SinglePDfDocumentExporter
    {
        #region EventsAndDelegates
        public event OnErrorDelegate OnError;
        #endregion

        public async Task<bool> ReadDataAndExportSinglePDFDocumentAsync(ExcelData data, string exportPdfPath)
        {
            try
            {
                await HtmlToPDFExporterTemplateAsync(
                    data.CompanyName,
                    data.CompanyLogoUrl,
                    data.ProductName,
                    data.ProductCode,
                    data.ProductSpecification,
                    data.ProductDescription,
                    data.ProductImageUrl,
                    data.ProductTechnicalImageUrl,
                    data.ProductImagesUrl,
                    data.CatalogueUrl,
                    data.Colour1CategoryTitle,
                    data.Colour2CategoryTitle,
                    data.Colour3CategoryTitle,
                    data.Colour1Image,
                    data.Colour2Image,
                    data.Colour3Image,
                    data.ProductImageColumnHeader,
                    data.TechnicalDrawingColumnHeader,
                    //Footer stuff
                    data.WebsiteUrl,
                    data.EmailAddress,
                    data.PhoneNumbers,
                    exportPdfPath);

                return true;
            }
            catch (Exception ex)
            {
                OnError?.Invoke(data.RowIndex, ex.Message);
                return false;
            }
        }

        private async Task HtmlToPDFExporterTemplateAsync(
            string company,
            string companyLogo,
            string productName,
            string productCode,
            string productSpecification,
            string productDescription,
            string productImageUrl,
            string productTechnicalImageUrl,
            List<string> productImagesUrl,
            string catalogue,
            string colour1CategoryTitle,
            string colour2CategoryTitle,
            string colour3CategoryTitle,
            string colour1Image,
            string colour2Image,
            string colour3Image,
            string productImageColumnHeader,
            string technicalDrawingColumnHeader,
            string websiteUrl,
            string emailAddress,
            string phoneNumbers,
            string path)
        {
            try
            {
                string templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PDFLayout", "template.html");
                string htmlTemplate = File.ReadAllText(templatePath);

                string htmlExtender = "";
                foreach (string imageUrl in productImagesUrl)
                {
                    htmlExtender += "<img class=\"img-fluid\" src=\"" + EncodeTheImage(imageUrl) + "\" alt=\"Additional Image\" />\r\n";
                }

                string companyLogoImg = !string.IsNullOrEmpty(companyLogo)
                    ? "<img src=\"" + EncodeTheImage(companyLogo) + "\" alt=\"Supplier Logo\" id=\"headerLogo\" />"
                    : "";

                // Detect which images are available
                bool hasProductImage = !string.IsNullOrEmpty(productImageUrl);
                bool hasTechnicalImage = !string.IsNullOrEmpty(productTechnicalImageUrl);
                bool hasAnyImage = hasProductImage || hasTechnicalImage;

                string productImg = hasProductImage
                    ? "<img src=\"" + EncodeTheImage(productImageUrl) + "\" class=\"img-fluid d-block\" />"
                    : "";

                string technicalImg = hasTechnicalImage
                    ? "<img src=\"" + EncodeTheImage(productTechnicalImageUrl) + "\" class=\"img-fluid d-block\" />"
                    : "";

                string footerLogoImg = "<img src=\"" + EncodeTheImage("images/bim4u.jpg") + "\" alt=\"BIM4U Logo\" id=\"footerLogo\" style=\"height:100px; max-width:100%; object-fit:contain; display:block; margin-left:auto;\">";

                productCode = string.IsNullOrEmpty(productCode) ? "N/A" : productCode;

                // Handle colour images and category titles
                string colour1CategoryTitleText = string.IsNullOrEmpty(colour1CategoryTitle) ? "ALUMINIUM COLOURS" : colour1CategoryTitle;
                string colour2CategoryTitleText = string.IsNullOrEmpty(colour2CategoryTitle) ? "NON SLIP INSERT COLOURS" : colour2CategoryTitle;
                string colour3CategoryTitleText = string.IsNullOrEmpty(colour3CategoryTitle) ? "ADDITIONAL COLOURS" : colour3CategoryTitle;

                string colour1ImageHtml = !string.IsNullOrEmpty(colour1Image)
                    ? "<img src=\"" + EncodeTheImage(colour1Image) + "\" alt=\"Colour 1\" class=\"colour-image\" />"
                    : "<img src=\"images/placeholder-product.jpg\" alt=\"Colour 1\" class=\"colour-image\" />";

                string colour2ImageHtml = !string.IsNullOrEmpty(colour2Image)
                    ? "<img src=\"" + EncodeTheImage(colour2Image) + "\" alt=\"Colour 2\" class=\"colour-image\" />"
                    : "<img src=\"images/placeholder-product.jpg\" alt=\"Colour 2\" class=\"colour-image\" />";

                string colour3ImageHtml = !string.IsNullOrEmpty(colour3Image)
                    ? "<img src=\"" + EncodeTheImage(colour3Image) + "\" alt=\"Colour 3\" class=\"colour-image\" />"
                    : "";

                // Handle conditional display of third color column
                string colour3CategoryDisplay = !string.IsNullOrEmpty(colour3Image) || !string.IsNullOrEmpty(colour3CategoryTitle) ? "block" : "none";
                string colour3ImagesDisplay = !string.IsNullOrEmpty(colour3Image) ? "block" : "none";

                // Generate dynamic image section title based on available images
                string imageSectionTitle = "";
                if (hasProductImage && hasTechnicalImage)
                {
                    imageSectionTitle = $"{productImageColumnHeader ?? "Product Image"} and {technicalDrawingColumnHeader ?? "Technical Drawing"}";
                }
                else if (hasProductImage)
                {
                    imageSectionTitle = productImageColumnHeader ?? "Product Image";
                }
                else if (hasTechnicalImage)
                {
                    imageSectionTitle = technicalDrawingColumnHeader ?? "Technical Drawing";
                }

                // Generate responsive HTML layout for images
                string imageBoxDisplay = hasAnyImage ? "block" : "none";
                string productImageColumnClass = "";
                string technicalImageColumnClass = "";

                if (hasProductImage && hasTechnicalImage)
                {
                    // Both images present - use col-6 for each
                    productImageColumnClass = "col-6";
                    technicalImageColumnClass = "col-6";
                }
                else if (hasProductImage && !hasTechnicalImage)
                {
                    // Only product image - use col-12 for full width
                    productImageColumnClass = "col-12";
                    technicalImageColumnClass = "d-none"; // Hide the technical image column
                }
                else if (!hasProductImage && hasTechnicalImage)
                {
                    // Only technical image - use col-12 for full width
                    productImageColumnClass = "d-none"; // Hide the product image column
                    technicalImageColumnClass = "col-12";
                }

                // Handle contact information with fallback to original hardcoded values
                string finalWebsiteUrl = !string.IsNullOrEmpty(websiteUrl) ? EnsureUrlHasProtocol(websiteUrl) : "https://www.kirk.co.za";
                string finalWebsiteDisplay = !string.IsNullOrEmpty(websiteUrl) ? websiteUrl.Replace("https://", "").Replace("http://", "") : "www.kirk.co.za";
                string finalEmailAddress = !string.IsNullOrEmpty(emailAddress) ? emailAddress : "<EMAIL>";
                string finalPhoneNumbers = !string.IsNullOrEmpty(phoneNumbers) ? phoneNumbers : "JHB: (+27) ************ &nbsp;|&nbsp;&nbsp; CT: (+27) ************ &nbsp;|&nbsp;&nbsp; DBN: (+27) ************ &nbsp;|&nbsp;&nbsp; PE: (+27) 41 010 0086 / ************";

                string finalHtml = htmlTemplate
                    .Replace("{{CompanyLogo}}", companyLogoImg)
                    .Replace("{{ProductName}}", productName)
                    .Replace("{{ProductCode}}", productCode)
                    .Replace("{{ProductDescription}}", productDescription)
                    .Replace("{{ProductSpecification}}", productSpecification)
                    .Replace("{{CompanyName}}", company)
                    .Replace("{{ProductImage}}", productImg)
                    .Replace("{{TechnicalDrawing}}", technicalImg)
                    .Replace("{{ExtraImages}}", htmlExtender)
                    .Replace("{{FooterLogo}}", footerLogoImg)
                    .Replace("{{Colour1CategoryTitle}}", colour1CategoryTitleText)
                    .Replace("{{Colour2CategoryTitle}}", colour2CategoryTitleText)
                    .Replace("{{Colour3CategoryTitle}}", colour3CategoryTitleText)
                    .Replace("{{Colour1Image}}", colour1ImageHtml)
                    .Replace("{{Colour2Image}}", colour2ImageHtml)
                    .Replace("{{Colour3Image}}", colour3ImageHtml)
                    .Replace("{{Colour3CategoryDisplay}}", colour3CategoryDisplay)
                    .Replace("{{Colour3ImagesDisplay}}", colour3ImagesDisplay)
                    .Replace("{{ImageSectionTitle}}", imageSectionTitle)
                    .Replace("{{ImageBoxDisplay}}", imageBoxDisplay)
                    .Replace("{{ProductImageColumnClass}}", productImageColumnClass)
                    .Replace("{{TechnicalImageColumnClass}}", technicalImageColumnClass)
                    .Replace("{{WebsiteUrl}}", finalWebsiteUrl)
                    .Replace("{{WebsiteDisplay}}", finalWebsiteDisplay)
                    .Replace("{{EmailAddress}}", finalEmailAddress)
                    .Replace("{{PhoneNumbers}}", finalPhoneNumbers);

                ConversionOptions options = new ConversionOptions(PageSize.A4, PageOrientation.Portrait);

                // Set explicit background color for consistent grey background across all pages
                options.PrintBackground = true;
                options.BackgroundColor = System.Drawing.Color.FromArgb(166, 169, 171); // #a6a9ab

                await Converter.ConvertAsync(finalHtml, path, null, options);
            }
            catch (Exception ex)
            {
                throw new Exception("Failed to generate PDF: " + ex.Message, ex);
            }
        }

        private string EncodeTheImage(string filePath)
        {
            try
            {
                byte[] imageBytes = File.ReadAllBytes(filePath);
                return "data:image/jpeg;base64," + Convert.ToBase64String(imageBytes);
            }
            catch
            {
                return "";
            }
        }

        private string EnsureUrlHasProtocol(string url)
        {
            if (string.IsNullOrEmpty(url))
                return url;

            // If URL already has protocol, return as-is
            if (url.StartsWith("http://") || url.StartsWith("https://"))
                return url;

            // Add https:// as default protocol
            return "https://" + url;
        }
    }
}


